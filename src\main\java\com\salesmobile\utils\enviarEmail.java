package com.salesmobile.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.salesmobile.ApiClient;
import com.salesmobile.config.ParametrosConf;

import org.json.JSONException;
import org.json.JSONObject;

public class enviarEmail {

    public interface EmailCallback {
        void onSuccess(String response);
        void onError(String errorMessage);
    }

    public static void enviarEmail(Context context, JSONObject response, boolean includeEmail, boolean includePdf, EmailCallback callback) {
        try {
            SharedPreferences prefs = context.getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
            String cuerpo = response.getString("cuerpo");
            String idComprobante = null;

            if (cuerpo.contains("IdComprobante:")) {
                int startIndex = cuerpo.indexOf("IdComprobante:") + "IdComprobante:".length();
                String remaining = cuerpo.substring(startIndex);
                StringBuilder sb = new StringBuilder();
                for (char c : remaining.toCharArray()) {
                    if (Character.isDigit(c)) {
                        sb.append(c);
                    } else {
                        break;
                    }
                }
                idComprobante = sb.toString();
            }

            String codPos = prefs.getString("puntoventa", "");
            String codEmp = prefs.getString("CodEmpresa", "");
            String codSuc = prefs.getString("CodSucursal", "");

            if (idComprobante == null || idComprobante.isEmpty()) {
                Log.d("EmailAPI", "IDCOMPROBANTE vacío");
                if (callback != null) {
                    callback.onError("IDCOMPROBANTE vacío");
                }
                return;
            }
            if (codPos.isEmpty()) {
                Log.d("EmailAPI", "Punto de venta no configurado");
                if (callback != null) {
                    callback.onError("Punto de venta no configurado");
                }
                return;
            }

            JSONObject json = new JSONObject();
            json.put("IDCOMPROBANTE", idComprobante);
            json.put("CODPOS", codPos);
            json.put("CODEMP", codEmp);
            json.put("CODSUC", codSuc);
            json.put("EMAIL", includeEmail ? "true" : "false");
            json.put("PDF", includePdf ? "true" : "false");

            if(ParametrosConf.TEST_MODE_VALUE.equals("true")) {
                json.put("TEST", "true");
                json.put("TESTPC", ParametrosConf.TEST_PC_VALUE);
            }

            String jsonBody = json.toString();
            Log.d("EmailAPI", "JSON enviado: " + jsonBody);


            ApiClient.getInstance().postWithBasicAuth(
                    ParametrosConf.BASE_URL + "enviarEmail.php",
                    jsonBody,
                    "smtp_user",
                    "K8mN2pQ7vX9wE3rT6yU1oI4hS5dF0gA8",
                    new ApiClient.ApiCallback() {
                        @Override
                        public void onSuccess(String emailResponse) {
                            Log.d("EmailAPI", "Email API exitoso: " + emailResponse);
                            if (callback != null) {
                                callback.onSuccess(emailResponse);
                            }
                        }

                        @Override
                        public void onError(String errorMessage) {
                            Log.d("EmailAPI", "Error en email API: " + errorMessage);
                            if (callback != null) {
                                callback.onError(errorMessage);
                            }
                        }
                    }
            );

        } catch (JSONException e) {
            Log.e("EmailAPI", "Error creating email request: " + e.getMessage());
            if (callback != null) {
                callback.onError("Error creating email request: " + e.getMessage());
            }
        }
    }

}
